import { useEffect, useRef, useState } from "react";
import ChatMessage from "@/components/ChatMessage";
import MessageInput from "@/components/MessageInput";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";

interface ChatInterfaceProps {
  messages: any[];
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
}

const ChatInterface = ({
  messages,
  onSendMessage,
  isLoading = false,
}: ChatInterfaceProps) => {
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const isAutoScrolling = useRef(false);

  // Helper function to check if user is at bottom
  const isAtBottom = () => {
    if (!chatContainerRef.current) return true;
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    return scrollHeight - scrollTop - clientHeight < 50; // 50px threshold
  };

  // Helper function to scroll to bottom smoothly
  const scrollToBottom = (smooth: boolean = false) => {
    if (chatContainerRef.current) {
      isAutoScrolling.current = true;
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
      // Reset auto-scrolling flag after scroll completes
      setTimeout(
        () => {
          isAutoScrolling.current = false;
          setUserHasScrolled(false);
          setShowScrollToBottom(false);
        },
        smooth ? 300 : 50
      );
    }
  };

  // Handle scroll events to detect user scrolling
  const handleScroll = () => {
    if (isAutoScrolling.current) return;

    const atBottom = isAtBottom();
    setUserHasScrolled(!atBottom);

    // Show scroll to bottom button if user scrolled up and there's activity (loading or recent messages)
    setShowScrollToBottom(!atBottom && (isLoading || messages.length > 0));
  };

  // Scroll to bottom when messages change (only if user hasn't scrolled up)
  useEffect(() => {
    if (messages && !userHasScrolled) {
      scrollToBottom();
    } else if (messages && userHasScrolled) {
      // If user has scrolled up and there are new messages, show the scroll to bottom button
      setShowScrollToBottom(true);
    }
  }, [messages, userHasScrolled]);

  // Update scroll to bottom button visibility when loading state changes
  useEffect(() => {
    if (chatContainerRef.current) {
      const atBottom = isAtBottom();
      setShowScrollToBottom(!atBottom && (isLoading || messages.length > 0));
    }
  }, [isLoading, userHasScrolled, messages.length]);

  return (
    <div className="flex flex-col h-full relative">
      {/* Chat Messages */}
      <div
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto chat-container px-2 sm:px-0"
        onScroll={handleScroll}
      >
        {messages.length > 0 && (
          <div className="py-4 sm:py-6 relative">
            {messages.map((message: any, index: number) => (
              <div key={message.id} className="relative">
                <ChatMessage message={message} />
                {/* Show scroll to bottom button on the last message if it's streaming/loading and user has scrolled */}
                {showScrollToBottom &&
                  index === messages.length - 1 &&
                  (message.metadata?.isStreaming || isLoading) && (
                    <Button
                      onClick={() => scrollToBottom(true)}
                      className="scroll-to-bottom-btn absolute -bottom-2 right-4 z-50 h-7 w-7 sm:h-7 sm:w-7 rounded-full p-0 shadow-lg bg-primary hover:bg-primary/90 border border-primary-foreground/20"
                      title="Scroll to bottom"
                    >
                      <ChevronDown className="h-3 w-3 sm:h-3 sm:w-3" />
                    </Button>
                  )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="p-3 sm:p-4">
        <MessageInput
          onSendMessage={onSendMessage}
          isLoading={isLoading}
          compact={false}
        />
      </div>
    </div>
  );
};

export default ChatInterface;
